[{"E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\(auth)\\signin\\page.tsx": "1", "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "2", "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\layout.tsx": "3", "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\page.tsx": "4", "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\lib\\apiClient.ts": "5", "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\types\\next-auth.d.ts": "6", "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\components\\providers\\sessionProvider.tsx": "7"}, {"size": 5931, "mtime": 1754206504766, "results": "8", "hashOfConfig": "9"}, {"size": 6228, "mtime": 1754206739687, "results": "10", "hashOfConfig": "9"}, {"size": 806, "mtime": 1754205187176, "results": "11", "hashOfConfig": "9"}, {"size": 4093, "mtime": 1754184291571, "results": "12", "hashOfConfig": "9"}, {"size": 1463, "mtime": 1754206781202, "results": "13", "hashOfConfig": "9"}, {"size": 447, "mtime": 1754205846577, "results": "14", "hashOfConfig": "9"}, {"size": 202, "mtime": 1754206522706, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yh8mt5", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\(auth)\\signin\\page.tsx", [], [], "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], ["37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47"], "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\layout.tsx", [], [], "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\app\\page.tsx", [], [], "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\lib\\apiClient.ts", [], ["48", "49"], "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\types\\next-auth.d.ts", [], [], "E:\\Code\\Realhub-Repos\\mrh-platform\\frontend\\src\\components\\providers\\sessionProvider.tsx", [], [], {"ruleId": "50", "severity": 2, "message": "51", "line": 105, "column": 25, "nodeType": "52", "messageId": "53", "endLine": 105, "endColumn": 28, "suggestions": "54", "suppressions": "55"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 140, "column": 27, "nodeType": "52", "messageId": "53", "endLine": 140, "endColumn": 30, "suggestions": "56", "suppressions": "57"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 161, "column": 20, "nodeType": "52", "messageId": "53", "endLine": 161, "endColumn": 23, "suggestions": "58", "suppressions": "59"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 162, "column": 20, "nodeType": "52", "messageId": "53", "endLine": 162, "endColumn": 23, "suggestions": "60", "suppressions": "61"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 162, "column": 49, "nodeType": "52", "messageId": "53", "endLine": 162, "endColumn": 52, "suggestions": "62", "suppressions": "63"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 163, "column": 20, "nodeType": "52", "messageId": "53", "endLine": 163, "endColumn": 23, "suggestions": "64", "suppressions": "65"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 171, "column": 21, "nodeType": "52", "messageId": "53", "endLine": 171, "endColumn": 24, "suggestions": "66", "suppressions": "67"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 172, "column": 22, "nodeType": "52", "messageId": "53", "endLine": 172, "endColumn": 25, "suggestions": "68", "suppressions": "69"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 172, "column": 52, "nodeType": "52", "messageId": "53", "endLine": 172, "endColumn": 55, "suggestions": "70", "suppressions": "71"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 174, "column": 29, "nodeType": "52", "messageId": "53", "endLine": 174, "endColumn": 32, "suggestions": "72", "suppressions": "73"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 174, "column": 49, "nodeType": "52", "messageId": "53", "endLine": 174, "endColumn": 52, "suggestions": "74", "suppressions": "75"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 19, "column": 21, "nodeType": "52", "messageId": "53", "endLine": 19, "endColumn": 24, "suggestions": "76", "suppressions": "77"}, {"ruleId": "50", "severity": 2, "message": "51", "line": 22, "column": 46, "nodeType": "52", "messageId": "53", "endLine": 22, "endColumn": 49, "suggestions": "78", "suppressions": "79"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["80", "81"], ["82"], ["83", "84"], ["85"], ["86", "87"], ["88"], ["89", "90"], ["91"], ["92", "93"], ["94"], ["95", "96"], ["97"], ["98", "99"], ["100"], ["101", "102"], ["103"], ["104", "105"], ["106"], ["107", "108"], ["109"], ["110", "111"], ["112"], ["113", "114"], ["115"], ["116", "117"], ["118"], {"messageId": "119", "fix": "120", "desc": "121"}, {"messageId": "122", "fix": "123", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "127", "desc": "121"}, {"messageId": "122", "fix": "128", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "129", "desc": "121"}, {"messageId": "122", "fix": "130", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "131", "desc": "121"}, {"messageId": "122", "fix": "132", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "133", "desc": "121"}, {"messageId": "122", "fix": "134", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "135", "desc": "121"}, {"messageId": "122", "fix": "136", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "137", "desc": "121"}, {"messageId": "122", "fix": "138", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "139", "desc": "121"}, {"messageId": "122", "fix": "140", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "141", "desc": "121"}, {"messageId": "122", "fix": "142", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "143", "desc": "121"}, {"messageId": "122", "fix": "144", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "145", "desc": "121"}, {"messageId": "122", "fix": "146", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "147", "desc": "121"}, {"messageId": "122", "fix": "148", "desc": "124"}, {"kind": "125", "justification": "126"}, {"messageId": "119", "fix": "149", "desc": "121"}, {"messageId": "122", "fix": "150", "desc": "124"}, {"kind": "125", "justification": "126"}, "suggestUnknown", {"range": "151", "text": "152"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "153", "text": "154"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", {"range": "155", "text": "152"}, {"range": "156", "text": "154"}, {"range": "157", "text": "152"}, {"range": "158", "text": "154"}, {"range": "159", "text": "152"}, {"range": "160", "text": "154"}, {"range": "161", "text": "152"}, {"range": "162", "text": "154"}, {"range": "163", "text": "152"}, {"range": "164", "text": "154"}, {"range": "165", "text": "152"}, {"range": "166", "text": "154"}, {"range": "167", "text": "152"}, {"range": "168", "text": "154"}, {"range": "169", "text": "152"}, {"range": "170", "text": "154"}, {"range": "171", "text": "152"}, {"range": "172", "text": "154"}, {"range": "173", "text": "152"}, {"range": "174", "text": "154"}, {"range": "175", "text": "152"}, {"range": "176", "text": "154"}, {"range": "177", "text": "152"}, {"range": "178", "text": "154"}, [3555, 3558], "unknown", [3555, 3558], "never", [4998, 5001], [4998, 5001], [5539, 5542], [5539, 5542], [5581, 5584], [5581, 5584], [5610, 5613], [5610, 5613], [5648, 5651], [5648, 5651], [5812, 5815], [5812, 5815], [5855, 5858], [5855, 5858], [5885, 5888], [5885, 5888], [5961, 5964], [5961, 5964], [5981, 5984], [5981, 5984], [553, 556], [553, 556], [675, 678], [675, 678]]